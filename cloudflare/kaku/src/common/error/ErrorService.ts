import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './ErrorCollector';
import { <PERSON>rrorRouter } from './ErrorRouter';
import { ErrorContext, ProcessedError, ErrorSource, LogLevel } from './types';
import { ErrorCode, SupportedLocales } from '../web/localizationMessages';

export class ErrorService {
  static async handleErrorWithNotification(errorContext: ErrorContext, env: Env): Promise<void> {
    const processedError = this.processErrorForUI(errorContext);
    const agentName = `${errorContext.userId}:${errorContext.platformId}`;

    try {
      const agentId = env.Connections.idFromName(agentName);
      const agent = env.Connections.get(agentId);

      await agent.handleAndDisplayError(errorContext);
      console.log(
        `[ErrorService] [DO: ${agentName}] Error communicated to agent:`,
        processedError.userMessage,
      );
    } catch (notificationError) {
      console.error(`[ErrorService] [DO: ${agentName}] Failed to notify agent:`, notificationError);
    }
  }

  static async handleBrowserConnectionError(
    originalError: Error,
    sessionContext: {
      sessionId: string;
      userId: string;
      platformId: string;
    },
    env: Env,
  ): Promise<never> {
    const errorContext = ErrorCollector.collectError(
      'browser_connection',
      'BROWSER_CONNECTION_FAILED',
      {
        error: {
          message: originalError.message,
          stack: originalError.stack,
          name: originalError.name,
        },
      },
      'error',
      sessionContext,
    );
    await this.handleErrorWithNotification(errorContext, env);
    throw errorContext;
  }

  static async handleScriptInjectionError(
    scriptName: string,
    originalError: Error,
    sessionContext: {
      sessionId: string;
      userId: string;
      platformId: string;
    },
    env: Env,
  ): Promise<never> {
    const errorContext = ErrorCollector.collectClientScriptError(
      scriptName,
      'SCRIPT_INJECTION_FAILED',
      originalError,
      sessionContext,
    );
    await this.handleErrorWithNotification(errorContext, env);
    throw errorContext;
  }

  static async handleWorkflowStepError(
    stepName: string,
    originalError: any,
    sessionContext: {
      sessionId: string;
      userId: string;
      platformId: string;
    },
    env: Env,
  ): Promise<never> {
    const errorContext = ErrorCollector.collectWorkflowError(
      stepName,
      'WORKFLOW_STEP_FAILED',
      originalError,
      sessionContext,
    );
    await this.handleErrorWithNotification(errorContext, env);
    throw errorContext;
  }

  static processErrorForUI(
    errorContext: ErrorContext,
    locale: SupportedLocales = 'en',
  ): ProcessedError {
    const classifiedError = ErrorRouter.classifyError(errorContext);
    return ErrorRouter.processError(classifiedError, locale);
  }

  static async handleGenericError(
    source: ErrorSource,
    errorCode: ErrorCode,
    originalError: any,
    sessionContext: {
      sessionId: string;
      userId: string;
      platformId: string;
      step?: string;
    },
    env: Env,
    logLevel: LogLevel = 'error',
  ): Promise<never> {
    const errorContext = ErrorCollector.collectError(
      source,
      errorCode,
      originalError,
      logLevel,
      sessionContext,
    );
    await this.handleErrorWithNotification(errorContext, env);
    throw errorContext;
  }
}

/**
 * Quick handler for common errors
 */
export const ErrorHandlers = {
  browserControllerInit: (error: Error, sessionContext: any, env: Env) =>
    ErrorService.handleScriptInjectionError('browser-controller', error, sessionContext, env),

  screenCropperInit: (error: Error, sessionContext: any, env: Env) =>
    ErrorService.handleGenericError(
      'screen_cropper',
      'SCREEN_CROPPER_INIT_FAILED',
      error,
      sessionContext,
      env,
      'error',
    ),

  captchaDetectorInit: (error: Error, sessionContext: any, env: Env) =>
    ErrorService.handleGenericError(
      'captcha_detection',
      'CAPTCHA_DETECTOR_INIT_FAILED',
      error,
      sessionContext,
      env,
      'error',
    ),

  websocketConnection: (error: Error, sessionContext: any, env: Env) =>
    ErrorService.handleGenericError(
      'network',
      'WEBSOCKET_CONNECTION_FAILED',
      error,
      sessionContext,
      env,
      'error',
    ),
};
