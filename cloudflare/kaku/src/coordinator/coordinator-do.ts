import { Agent, AgentContext } from 'agents';
import { Environment } from '../common/types';
import { PlatformTypes } from '../ui/constants';
import {
  CoordinatorState,
  PlatformMetadata,
  LinkInfo,
  CoordinatorUtils,
  COORDINATOR_CONSTANTS,
  CreateLinkResponse,
} from '../shared/coordinator-types';

/**
 * CoordinatorDO - Manages links and Agent DOs for platform connections
 *
 * This Agent serves as a central coordinator for managing
 * links, Agent DO lifecycle, and platform-specific retry logic.
 */
export class CoordinatorDO extends Agent<Environment, CoordinatorState> {
  initialState: CoordinatorState = {
    platforms: [],
  };

  constructor(ctx: AgentContext, env: Environment) {
    super(ctx, env);
  }

  /**
   * Get or create platform metadata
   */
  private getOrCreatePlatform(platformId: PlatformTypes): PlatformMetadata {
    let platform = this.state.platforms.find((p) => p.id === platformId);

    if (!platform) {
      platform = {
        id: platformId,
        links: [],
        connected: false,
        retryCount: 0,
      };

      this.setState({
        platforms: [...this.state.platforms, platform],
      });
    }

    return platform;
  }

  // RPC Methods for link management

  /**
   * Create a new link for platform connection
   */
  async createLink(platform: PlatformTypes, account: string): Promise<CreateLinkResponse> {
    const platformData = this.getOrCreatePlatform(platform);

    if (platformData.connected) {
      throw new Error(`User is already connected to ${platform}`);
    }

    // Check if retries are exhausted for this platform
    if (!CoordinatorUtils.canRetry(platformData.retryCount)) {
      throw new Error(`Retry limit exceeded for ${platform}`);
    }

    // TODO: Generate unique link ID and URL
    const linkId = CoordinatorUtils.generateLinkId();
    const url = `https://kazeel.com/connect/${linkId}`;
    const now = Date.now();
    const expiresAt = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;

    const agentId = await this.createAgentDO(linkId, platform, account);

    const linkInfo: LinkInfo = {
      status: 'active',
      url,
      createdAt: now,
      expiresAt,
      agentId: agentId,
    };

    const updatedPlatforms = this.state.platforms.map((p) =>
      p.id === platform ? { ...p, links: [...p.links, linkInfo] } : p,
    );

    this.setState({
      platforms: updatedPlatforms,
    });

    await this.schedule(new Date(expiresAt), 'handleLinkExpiration', linkId);

    return { linkId, url, expiresAt };
  }

  /**
   * Get status of a specific link
   */
  async getStatus(linkId: string): Promise<LinkInfo | null> {
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        if (Date.now() > link.expiresAt && link.status === 'active') {
          await this.expireLink(linkId);
          return { ...link, status: 'expired' };
        }
        return link;
      }
    }
    return null;
  }

  /**
   * Handle link expiration (called by scheduler)
   */
  async handleLinkExpiration(linkId: string): Promise<void> {
    console.log(`[CoordinatorDO] Handling expiration for link: ${linkId}`);
    await this.expireLink(linkId);

    // Schedule deletion after 7 days
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Handle link deletion (called by scheduler)
   */
  async handleLinkDeletion(linkId: string): Promise<void> {
    console.log(`[CoordinatorDO] Handling deletion for link: ${linkId}`);
  }

  // Private helper methods

  /**
   * Create a new Agent DO for the link
   */
  private async createAgentDO(
    linkId: string,
    platform: PlatformTypes,
    _account: string,
  ): Promise<string> {
    // Agent DO will use linkId as its identifier instead of userId:platformId
    const agentId = linkId;

    const agentStub = this.env.Connections.idFromName(agentId);
    const agent = this.env.Connections.get(agentStub);
    await agent.setName(agentId);
    console.log(`[CoordinatorDO] Created Agent DO: ${agentId} for platform: ${platform}`);
    // schedule for agent DO deletion
    await this.schedule(
      new Date(Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY),
      'deleteAgentDO',
      agentId,
    );
    return agentId;
  }

  /**
   * Delete an Agent DO
   */
  async deleteAgentDO(agentId: string): Promise<void> {
    try {
      const agentStub = this.env.Connections.idFromName(agentId);
      const agent = this.env.Connections.get(agentStub);

      // Delete all data from the Agent DO
      await agent.deleteAll();

      console.log(`[CoordinatorDO] Deleted Agent DO: ${agentId}`);
    } catch (error) {
      console.error(`[CoordinatorDO] Failed to delete Agent DO ${agentId}:`, error);
    }
  }

  /**
   * Expire a link and schedule deletion
   */
  private async expireLink(linkId: string): Promise<void> {
    // Find and update the link status across all platforms
    const updatedPlatforms = this.state.platforms.map((platform) => ({
      ...platform,
      links: platform.links.map((link) =>
        link.url.includes(linkId) ? { ...link, status: 'expired' as const } : link,
      ),
    }));

    this.setState({
      platforms: updatedPlatforms,
    });
  }

  /**
   * Reset a link by creating a new Agent DO (retry functionality)
   */
  async resetLink(linkId: string): Promise<{ newAgentId: string } | null> {
    let targetPlatform: PlatformMetadata | null = null;
    let targetLink: LinkInfo | null = null;

    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        targetPlatform = platform;
        targetLink = link;
        break;
      }
    }

    if (!targetPlatform || !targetLink || targetLink.status !== 'active') {
      return null;
    }

    if (!CoordinatorUtils.canRetry(targetPlatform.retryCount)) {
      throw new Error(`Retry limit exceeded for ${targetPlatform.id}`);
    }

    const newAgentId = await this.createAgentDO(linkId, targetPlatform.id, 'account');

    const updatedPlatforms = this.state.platforms.map((platform) => {
      if (platform.id === targetPlatform!.id) {
        return {
          ...platform,
          retryCount: platform.retryCount + 1,
          links: platform.links.map((link) =>
            link.url.includes(linkId)
              ? {
                  ...link,
                  agentId: newAgentId,
                }
              : link,
          ),
        };
      }
      return platform;
    });

    this.setState({
      platforms: updatedPlatforms,
    });

    console.log(`[CoordinatorDO] Reset link ${linkId} with new Agent DO: ${newAgentId}`);
    return { newAgentId };
  }

  /**
   * Mark a platform as connected for this user
   */
  async markPlatformConnected(linkId: string): Promise<void> {
    const updatedPlatforms = this.state.platforms.map((platform) => {
      const linkIndex = platform.links.findIndex((l) => l.url.includes(linkId));
      if (linkIndex !== -1) {
        const updatedLinks = [...platform.links];
        updatedLinks[linkIndex] = {
          ...updatedLinks[linkIndex],
          status: 'connected',
          connectedAt: Date.now(),
        };

        return {
          ...platform,
          connected: true,
          links: updatedLinks,
        };
      }
      return platform;
    });

    this.setState({
      platforms: updatedPlatforms,
    });

    // Schedule deletion right away for connected links (7 days)
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Get Agent DO identifier for a link
   */
  getAgentIdForLink(linkId: string): string | null {
    // Find the link across all platforms
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        return link.agentId || null;
      }
    }
    return null;
  }
}
